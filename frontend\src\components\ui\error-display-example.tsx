import React, { useState } from 'react';
import { ErrorDisplay, type ErrorData } from './error-display';
import { Button } from './button';

/**
 * Example component showing how to use the ErrorDisplay component
 * This file can be deleted - it's just for demonstration
 */
export const ErrorDisplayExample: React.FC = () => {
  const [error, setError] = useState<ErrorData | null>(null);

  const simulateValidationError = () => {
    setError({
      message: "Your request is not valid!",
      details: "The following errors were detected during validation.\n - The input field is required.\n - Can't get datetime from the reader!\n",
      validationErrors: [
        {
          message: "The input field is required.",
          members: ["input"]
        },
        {
          message: "Can't get datetime from the reader!",
          members: ["$.vesselArrival"]
        },
        {
          message: "Invalid port selection.",
          members: ["$.destinationPortId"]
        },
        {
          message: "Quantity must be greater than zero.",
          members: ["$.items[0].quantity"]
        }
      ]
    });
  };

  const simulateSimpleError = () => {
    setError({
      message: "Network connection failed. Please try again."
    });
  };

  const simulateComplexError = () => {
    setError({
      message: "Multiple validation failures occurred",
      details: "The request contained several validation errors:\n - Missing required field: vessel name\n - Invalid date format in arrival date\n - Duplicate item entries found\n - Business partner not found",
      validationErrors: [
        {
          message: "Vessel name is required",
          members: ["vesselId", "vessel.name"]
        },
        {
          message: "Invalid date format",
          members: ["vesselArrival", "$.postingDate"]
        },
        {
          message: "Duplicate entries not allowed",
          members: ["items", "$.items[2].itemCode"]
        },
        {
          message: "Business partner does not exist",
          members: ["businessPartnerId", "$.businessPartner"]
        },
        {
          message: "Port of loading is required",
          members: ["portOriginId"]
        },
        {
          message: "Destination port is invalid",
          members: ["destinationPortId"]
        }
      ]
    });
  };

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold">Error Display Component Examples</h2>
      
      <div className="space-x-2">
        <Button onClick={simulateValidationError}>
          Simulate Validation Error (2 errors)
        </Button>
        <Button onClick={simulateSimpleError} variant="outline">
          Simulate Simple Error
        </Button>
        <Button onClick={simulateComplexError} variant="outline">
          Simulate Complex Error (6 errors)
        </Button>
        <Button onClick={() => setError(null)} variant="destructive">
          Clear Error
        </Button>
      </div>

      {error && (
        <ErrorDisplay
          error={error}
          onDismiss={() => setError(null)}
          maxVisibleErrors={2}
        />
      )}

      <div className="bg-card p-4 rounded-lg border">
        <h3 className="font-medium mb-2">Sample Form Content</h3>
        <p className="text-sm text-gray-600">
          This represents your form content. The error display appears above this card.
        </p>
      </div>
    </div>
  );
};

export default ErrorDisplayExample;
