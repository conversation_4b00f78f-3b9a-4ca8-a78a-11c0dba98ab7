import React, { useState } from 'react';
import { X, ChevronDown, ChevronUp, AlertCircle, Info } from 'lucide-react';
import { Button } from './button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './dialog';

export interface ValidationError {
  message: string;
  members: string[];
}

export interface ErrorData {
  message?: string;
  details?: string;
  validationErrors?: ValidationError[];
}

interface ErrorDisplayProps {
  error: ErrorData;
  onDismiss: () => void;
  maxVisibleErrors?: number;
  className?: string;
}

// Error details dialog component
const ErrorDetailsDialog: React.FC<{ error: ErrorData }> = ({ error }) => {
  const formatFieldName = (field?: string): string => {
    if (!field) return '';

    // Convert camelCase to readable format and clean up JSON path notation
    const cleaned = field.replace('$.', '').replace(/\[(\d+)\]/g, ' [$1]');
    const readable = cleaned
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();

    return readable;
  };

  return (
    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center text-red-700">
          <AlertCircle className="w-5 h-5 mr-2" />
          Error Details
        </DialogTitle>
        <DialogDescription>
          Complete information about the validation errors encountered.
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4">
        {error.message && error.message !== 'Your request is not valid!' && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Error Message</h4>
            <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">{error.message}</p>
          </div>
        )}

        {error.validationErrors && error.validationErrors.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Validation Errors</h4>
            <div className="space-y-3">
              {error.validationErrors.map((validationError, index) => (
                <div key={index} className="border border-red-200 bg-red-50 p-3 rounded">
                  <p className="text-sm text-red-800 font-medium mb-1">
                    {validationError.message}
                  </p>
                  {validationError.members && validationError.members.length > 0 && (
                    <div className="text-xs text-red-600">
                      <span className="font-medium">Affected field(s): </span>
                      {validationError.members.map((member, memberIndex) => (
                        <span key={memberIndex} className="font-mono bg-red-100 px-1 py-0.5 rounded mr-1">
                          {formatFieldName(member)}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {error.details && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Technical Details</h4>
            <pre className="text-xs text-gray-600 bg-gray-50 p-3 rounded whitespace-pre-wrap overflow-x-auto">
              {error.details}
            </pre>
          </div>
        )}
      </div>
    </DialogContent>
  );
};

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onDismiss,
  maxVisibleErrors = 2,
  className = ""
}) => {

  const formatErrorMessages = (error: ErrorData): Array<{ message: string; field?: string }> => {
    const messages: Array<{ message: string; field?: string }> = [];

    // Add validation errors with field information
    if (error.validationErrors && error.validationErrors.length > 0) {
      error.validationErrors.forEach(validationError => {
        const field = validationError.members && validationError.members.length > 0
          ? validationError.members[0].replace('$.', '')
          : undefined;

        messages.push({
          message: validationError.message,
          field: field
        });
      });
    }

    // If no validation errors, try to parse details
    if (messages.length === 0 && error.details) {
      const detailLines = error.details
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('The following errors were detected') && line !== '-')
        .map(line => line.replace(/^- /, '')); // Remove leading dash

      detailLines.forEach(line => {
        messages.push({ message: line });
      });
    }

    // Fallback to main message if no specific errors found
    if (messages.length === 0 && error.message && error.message !== 'Your request is not valid!') {
      messages.push({ message: error.message });
    }

    // Final fallback
    if (messages.length === 0) {
      messages.push({ message: 'An error occurred while processing your request.' });
    }

    return messages;
  };

  const errorMessages = formatErrorMessages(error);
  const visibleErrors = errorMessages.slice(0, maxVisibleErrors);
  const hasMoreErrors = errorMessages.length > maxVisibleErrors;
  const hasValidationDetails = error.validationErrors && error.validationErrors.some(ve => ve.members && ve.members.length > 0);

  const formatFieldName = (field?: string): string => {
    if (!field) return '';

    // Convert camelCase to readable format and clean up JSON path notation
    const cleaned = field.replace('$.', '').replace(/\[(\d+)\]/g, ' [$1]');
    const readable = cleaned
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();

    return readable;
  };

  return (
    <div className={`mb-4 bg-red-50 border border-red-200 rounded-lg p-4 relative ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mr-3 mt-0.5">
          <AlertCircle className="w-5 h-5 text-red-500" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <span className="text-red-800 font-medium text-sm">
              {errorMessages.length > 1
                ? `${errorMessages.length} errors were found:`
                : 'An error occurred:'
              }
            </span>
            <button
              onClick={onDismiss}
              className="text-red-400 hover:text-red-600 transition-colors p-1 rounded hover:bg-red-100"
              aria-label="Dismiss error"
            >
              <X size={16} />
            </button>
          </div>

          <div className="text-red-700 text-sm space-y-2">
            {visibleErrors.map((errorItem, index) => (
              <div key={index} className="flex items-start">
                <span className="mr-2 mt-1 text-red-500">•</span>
                <div className="flex-1">
                  <span>{errorItem.message}</span>
                </div>
              </div>
            ))}
          </div>

          {(hasMoreErrors || hasValidationDetails) && (
            <div className="mt-3 pt-2 border-t border-red-200 flex gap-2">
              {hasMoreErrors && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAllErrors(!showAllErrors)}
                  className="text-red-700 hover:text-red-800 hover:bg-red-100 p-2 h-auto"
                >
                  <span className="flex items-center text-xs">
                    {showAllErrors ? (
                      <>
                        <ChevronUp size={14} className="mr-1" />
                        Show Less
                      </>
                    ) : (
                      <>
                        <ChevronDown size={14} className="mr-1" />
                        Show {errorMessages.length - maxVisibleErrors} More Error{errorMessages.length - maxVisibleErrors > 1 ? 's' : ''}
                      </>
                    )}
                  </span>
                </Button>
              )}

              {(hasValidationDetails || error.details) && (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="text-red-700 hover:text-red-800 hover:bg-red-100 p-2 h-auto"
                    >
                      <span className="flex items-center text-xs">
                        <Info size={14} className="mr-1" />
                        Show Details
                      </span>
                    </Button>
                  </DialogTrigger>
                  <ErrorDetailsDialog error={error} />
                </Dialog>
              )}
            </div>
          )}

          {showAllErrors && hasMoreErrors && (
            <div className="mt-2 text-red-700 text-sm space-y-2">
              {errorMessages.slice(maxVisibleErrors).map((errorItem, index) => (
                <div key={index + maxVisibleErrors} className="flex items-start">
                  <span className="mr-2 mt-1 text-red-500">•</span>
                  <div className="flex-1">
                    <span>{errorItem.message}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
