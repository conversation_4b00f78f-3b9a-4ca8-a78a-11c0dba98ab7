using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Packaging;
using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.DocumentTemplates;
using Imip.JettyApproval.JettyRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// Service for application document generation
/// </summary>
[Authorize]
public class ApplicationDocumentService : ApplicationService, IApplicationDocumentService
{
    private readonly IRepository<JettyRequestItem, Guid> _jettyRequestItemRepository;
    private readonly IDocumentTemplateRepository _documentTemplateRepository;
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly IBlobContainer _blobContainer;
    private readonly ISyncfusionDocxToPdfService _syncfusionDocxToPdfService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public ApplicationDocumentService(
        IRepository<JettyRequestItem, Guid> jettyRequestItemRepository,
        IDocumentTemplateRepository documentTemplateRepository,
        IAttachmentRepository attachmentRepository,
        IAttachmentAppService attachmentAppService,
        IBlobContainer blobContainer,
        ISyncfusionDocxToPdfService syncfusionDocxToPdfService,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _jettyRequestItemRepository = jettyRequestItemRepository;
        _documentTemplateRepository = documentTemplateRepository;
        _attachmentRepository = attachmentRepository;
        _attachmentAppService = attachmentAppService;
        _blobContainer = blobContainer;
        _syncfusionDocxToPdfService = syncfusionDocxToPdfService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <summary>
    /// Generates an application document for a JettyRequestItem and saves it as an attachment
    /// </summary>
    public async Task<FileUploadResultDto> GenerateApplicationDocumentAsAttachmentAsync(ApplicationDocumentGenerationDto input)
    {
        // Generate the document file
        var fileDto = await GenerateApplicationDocumentAsync(input);

        // Get the jetty request item to use as reference
        var jettyRequestItem = await GetJettyRequestItemWithDetailsAsync(input.JettyRequestItemId);

        // Create file upload DTO
        var fileUploadDto = new FileUploadDto
        {
            Description = $"Application document for {jettyRequestItem.ItemName} - {jettyRequestItem.JettyRequest.VesselName}",
            ReferenceId = jettyRequestItem.Id,
            ReferenceType = "JettyRequestItem"
        };

        // Upload the file as an attachment
        var result = await _attachmentAppService.UploadFileAsync(
            fileUploadDto,
            fileDto.FileName,
            fileDto.ContentType,
            fileDto.Content);

        // Update the JettyRequestItem's ReferenceId to link to the attachment
        using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
        {
            try
            {
                jettyRequestItem.ReferenceId = result.Id;
                await _jettyRequestItemRepository.UpdateAsync(jettyRequestItem);
                await uow.CompleteAsync();
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                Logger.LogError(ex, "Error updating JettyRequestItem ReferenceId");
                throw;
            }
        }

        return result;
    }

    /// <summary>
    /// Generates an application document for a JettyRequestItem and returns the file content
    /// </summary>
    public async Task<FileDto> GenerateApplicationDocumentAsync(ApplicationDocumentGenerationDto input)
    {
        // Get the jetty request item with related entities
        var jettyRequestItem = await GetJettyRequestItemWithDetailsAsync(input.JettyRequestItemId);

        // Get the template (DocumentType.Letter = 4)
        DocumentTemplate template;
        if (input.TemplateId.HasValue)
        {
            template = await _documentTemplateRepository.GetAsync(input.TemplateId.Value);
        }
        else
        {
            template = await _documentTemplateRepository.GetDefaultTemplateAsync(DocumentType.Letter);
            if (template == null)
            {
                throw new UserFriendlyException(L["NoDefaultTemplateFound", DocumentType.Letter]);
            }
        }

        // Get the attachment (DOCX template)
        var attachment = await _attachmentRepository.GetAsync(template.AttachmentId);
        if (attachment?.BlobName == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Get the DOCX file from blob storage
        var docxStream = await _blobContainer.GetAsync(attachment.BlobName);
        if (docxStream == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Read the DOCX file into memory
        using var memoryStream = new MemoryStream();
        await docxStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        // Get the application data
        var applicationData = GetApplicationDocumentData(jettyRequestItem);

        // Process the template
        using (var wordDocument = WordprocessingDocument.Open(memoryStream, true))
        {
            var mainPart = wordDocument.MainDocumentPart;
            if (mainPart?.Document == null)
            {
                throw new UserFriendlyException(L["InvalidTemplateFile"]);
            }

            // Create placeholders dictionary
            var placeholders = CreatePlaceholdersDictionary(applicationData);

            // Replace placeholders in the document
            DocumentTextReplacer.ReplaceTextInDocument(mainPart.Document, placeholders);
        }

        // Generate filename
        var filename = GenerateFilename(input, applicationData);

        // Convert to PDF if requested
        if (input.GeneratePdf)
        {
            try
            {
                return await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(memoryStream.ToArray(), filename);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "PDF conversion failed for application document {Filename}. Returning DOCX instead.", filename);
                return new FileDto(
                    $"{filename}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    memoryStream.ToArray()
                );
            }
        }
        else
        {
            return new FileDto(
                $"{filename}.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                memoryStream.ToArray()
            );
        }
    }

    /// <summary>
    /// Generates an application document with payload data from frontend and returns the file content
    /// </summary>
    public async Task<FileDto> GenerateApplicationDocumentWithPayloadAsync(ApplicationDocumentGenerationWithPayloadDto input)
    {
        // Get the template (DocumentType.Letter = 4)
        DocumentTemplate template;
        if (input.TemplateId.HasValue)
        {
            template = await _documentTemplateRepository.GetAsync(input.TemplateId.Value);
        }
        else
        {
            template = await _documentTemplateRepository.GetDefaultTemplateAsync(DocumentType.Letter);
            if (template == null)
            {
                throw new UserFriendlyException(L["NoDefaultTemplateFound", DocumentType.Letter]);
            }
        }

        // Get the attachment (DOCX template)
        var attachment = await _attachmentRepository.GetAsync(template.AttachmentId);
        if (attachment?.BlobName == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Get the DOCX file from blob storage
        var docxStream = await _blobContainer.GetAsync(attachment.BlobName);
        if (docxStream == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Read the DOCX file into memory
        using var memoryStream = new MemoryStream();
        await docxStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        // Use the application data from the payload
        var applicationData = input.DocumentData;

        // Process the template
        using (var wordDocument = WordprocessingDocument.Open(memoryStream, true))
        {
            var mainPart = wordDocument.MainDocumentPart;
            if (mainPart?.Document == null)
            {
                throw new UserFriendlyException(L["InvalidTemplateFile"]);
            }

            // Create placeholders dictionary
            var placeholders = CreatePlaceholdersDictionary(applicationData);

            // Replace placeholders in the document
            DocumentTextReplacer.ReplaceTextInDocument(mainPart.Document, placeholders);
        }

        // Generate filename
        var filename = GenerateFilenameWithPayload(input, applicationData);

        // Convert to PDF if requested
        if (input.GeneratePdf)
        {
            try
            {
                return await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(memoryStream.ToArray(), filename);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "PDF conversion failed for application document {Filename}. Returning DOCX instead.", filename);
                return new FileDto(
                    $"{filename}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    memoryStream.ToArray()
                );
            }
        }
        else
        {
            return new FileDto(
                $"{filename}.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                memoryStream.ToArray()
            );
        }
    }

    /// <summary>
    /// Generates an application document with payload data from frontend and saves it as an attachment
    /// </summary>
    public async Task<FileUploadResultDto> GenerateApplicationDocumentWithPayloadAsAttachmentAsync(ApplicationDocumentGenerationWithPayloadDto input)
    {
        // Generate the document file
        var fileDto = await GenerateApplicationDocumentWithPayloadAsync(input);

        // Create file upload DTO
        var fileUploadDto = new FileUploadDto
        {
            Description = $"Application document for {input.DocumentData.ItemName} - {input.DocumentData.VesselName}",
            ReferenceId = Guid.Empty, // No specific reference since this is from EKB data
            ReferenceType = "EkbDocument"
        };

        // Upload the file as an attachment
        var result = await _attachmentAppService.UploadFileAsync(
            fileUploadDto,
            fileDto.FileName,
            fileDto.ContentType,
            fileDto.Content);

        return result;
    }

    /// <summary>
    /// Gets JettyRequestItem with all related entities
    /// </summary>
    private async Task<JettyRequestItem> GetJettyRequestItemWithDetailsAsync(Guid jettyRequestItemId)
    {
        var queryable = await _jettyRequestItemRepository.WithDetailsAsync(
            item => item.JettyRequest
        );

        var jettyRequestItem = queryable.FirstOrDefault(item => item.Id == jettyRequestItemId);
        if (jettyRequestItem == null)
        {
            throw new UserFriendlyException(L["JettyRequestItemNotFound"]);
        }

        return jettyRequestItem;
    }

    /// <summary>
    /// Creates application document data from JettyRequestItem
    /// </summary>
    private ApplicationDocumentDataDto GetApplicationDocumentData(JettyRequestItem jettyRequestItem)
    {
        return new ApplicationDocumentDataDto
        {
            // JettyRequestItem properties
            TenantName = jettyRequestItem.TenantName,
            ItemName = jettyRequestItem.ItemName,
            Qty = jettyRequestItem.Qty,
            UoM = jettyRequestItem.UoM,
            Notes = jettyRequestItem.Notes,
            Status = jettyRequestItem.Status,
            LetterNo = jettyRequestItem.LetterNo,
            LetterDate = jettyRequestItem.LetterDate,

            // JettyRequest properties
            DocNum = jettyRequestItem.JettyRequest.DocNum,
            VesselType = jettyRequestItem.JettyRequest.VesselType,
            VesselName = jettyRequestItem.JettyRequest.VesselName,
            Voyage = jettyRequestItem.JettyRequest.Voyage,
            Jetty = jettyRequestItem.JettyRequest.Jetty,
            ArrivalDate = jettyRequestItem.JettyRequest.ArrivalDate,
            DepartureDate = jettyRequestItem.JettyRequest.DepartureDate,
            AsideDate = jettyRequestItem.JettyRequest.AsideDate,
            CastOfDate = jettyRequestItem.JettyRequest.CastOfDate,
            PostDate = jettyRequestItem.JettyRequest.PostDate,
            Barge = jettyRequestItem.JettyRequest.Barge,
            PortOrigin = jettyRequestItem.JettyRequest.PortOrigin,
            DestinationPort = jettyRequestItem.JettyRequest.DestinationPort,

            // Additional computed fields
            GeneratedDate = DateTime.Now,
            GeneratedBy = CurrentUser?.UserName
        };
    }

    /// <summary>
    /// Creates placeholders dictionary for template replacement
    /// </summary>
    private Dictionary<string, string> CreatePlaceholdersDictionary(ApplicationDocumentDataDto data)
    {
        // Concatenate vessel name and voyage for VESSEL_NAME placeholder
        var vesselName = !string.IsNullOrEmpty(data.Voyage)
            ? $"{data.VesselName} - {data.Voyage}"
            : data.VesselName ?? "";

        return new Dictionary<string, string>
        {
            { "{{VESSEL_NAME}}", vesselName },
            { "{{ARRIVAL_DATE}}", data.ArrivalDate?.ToString("dd/MM/yyyy") ?? "" },
            { "{{PORT_ORIGIN}}", data.PortOrigin ?? "" },
            { "{{DESTINATION_PORT}}", data.DestinationPort ?? "" },
            { "{{ITEM_NAME}}", data.ItemName ?? "" },
            { "{{QTY}}", data.Qty.ToString("#,##0") },
            { "{{LETTER_NO}}", data.LetterNo ?? "" },
            { "{{LETTER_DATE}}", data.LetterDate?.ToString("dd MMMM yyyy") ?? "" },
            
            // Additional placeholders that might be useful
            { "{{TENANT_NAME}}", data.TenantName ?? "" },
            { "{{UOM}}", data.UoM ?? "" },
            { "{{NOTES}}", data.Notes ?? "" },
            { "{{DOC_NUM}}", data.DocNum.ToString() },
            { "{{VESSEL_TYPE}}", data.VesselType ?? "" },
            { "{{JETTY}}", data.Jetty ?? "" },
            { "{{DEPARTURE_DATE}}", data.DepartureDate?.ToString("dd/MM/yyyy") ?? "" },
            { "{{BARGE}}", data.Barge ?? "" },
            { "{{GENERATED_DATE}}", data.GeneratedDate.ToString("dd MMMM yyyy") },
            { "{{GENERATED_BY}}", data.GeneratedBy ?? "" }
        };
    }

    /// <summary>
    /// Generates filename for the document
    /// </summary>
    private string GenerateFilename(ApplicationDocumentGenerationDto input, ApplicationDocumentDataDto data)
    {
        if (!string.IsNullOrEmpty(input.CustomFilename))
        {
            return input.CustomFilename;
        }

        // Create a safe filename from vessel name and item name
        var vesselSlug = Regex.Replace(data.VesselName ?? "Unknown", @"[^a-zA-Z0-9]+", "_");
        var itemSlug = Regex.Replace(data.ItemName ?? "Item", @"[^a-zA-Z0-9]+", "_");

        return $"Application_{vesselSlug}_{itemSlug}_{DateTime.Now:yyyyMMdd_HHmmss}";
    }

    /// <summary>
    /// Generates filename for the document with payload data
    /// </summary>
    private string GenerateFilenameWithPayload(ApplicationDocumentGenerationWithPayloadDto input, ApplicationDocumentDataDto data)
    {
        if (!string.IsNullOrEmpty(input.CustomFilename))
        {
            return input.CustomFilename;
        }

        // Create a safe filename from vessel name and item name
        var vesselSlug = Regex.Replace(data.VesselName ?? "Unknown", @"[^a-zA-Z0-9]+", "_");
        var itemSlug = Regex.Replace(data.ItemName ?? "Item", @"[^a-zA-Z0-9]+", "_");

        return $"Application_{vesselSlug}_{itemSlug}_{DateTime.Now:yyyyMMdd_HHmmss}";
    }
}
