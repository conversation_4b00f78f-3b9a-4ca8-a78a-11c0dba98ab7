using System;
using System.Collections.Generic;
using System.Linq;
using Imip.JettyApproval.JettyRequests;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for JettyRequestItem entity
/// </summary>
[Mapper]
public partial class JettyRequestItemMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(JettyRequestItem.Id), nameof(JettyRequestItemDto.Id))]
    [MapperIgnoreSource(nameof(JettyRequestItem.IsDeleted))]
    [MapperIgnoreSource(nameof(JettyRequestItem.DeleterId))]
    [MapperIgnoreSource(nameof(JettyRequestItem.DeletionTime))]
    [MapperIgnoreSource(nameof(JettyRequestItem.LastModificationTime))]
    [MapperIgnoreSource(nameof(JettyRequestItem.LastModifierId))]
    [MapperIgnoreSource(nameof(JettyRequestItem.CreationTime))]
    [MapperIgnoreSource(nameof(JettyRequestItem.CreatorId))]
    [MapperIgnoreSource(nameof(JettyRequestItem.ExtraProperties))]
    [MapperIgnoreSource(nameof(JettyRequestItem.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(JettyRequestItem.JettyRequest))]
    [MapperIgnoreTarget(nameof(JettyRequestItemDto.JettyRequest))]
    private partial JettyRequestItemDto MapToDtoInternal(JettyRequestItem entity);

    // Public method with custom LetterDate formatting and JettyRequest mapping
    public JettyRequestItemDto MapToDto(JettyRequestItem entity)
    {
        var dto = MapToDtoInternal(entity);
        // Format LetterDate as Y-m-d
        dto.LetterDate = entity.LetterDate?.ToString("yyyy-MM-dd");

        // Map JettyRequest navigation property (without Items to avoid circular reference)
        if (entity.JettyRequest != null)
        {
            dto.JettyRequest = MapJettyRequestToDto(entity.JettyRequest);
        }

        return dto;
    }

    // Helper method to map JettyRequest without Items (to avoid circular dependency)
    private JettyRequestDto MapJettyRequestToDto(JettyRequest jettyRequest)
    {
        return new JettyRequestDto
        {
            Id = jettyRequest.Id,
            DocNum = jettyRequest.DocNum,
            VesselType = jettyRequest.VesselType,
            ReferenceId = jettyRequest.ReferenceId,
            VesselName = jettyRequest.VesselName,
            Voyage = jettyRequest.Voyage,
            Jetty = jettyRequest.Jetty,
            ArrivalDate = jettyRequest.ArrivalDate,
            DepartureDate = jettyRequest.DepartureDate,
            AsideDate = jettyRequest.AsideDate,
            CastOfDate = jettyRequest.CastOfDate,
            PostDate = jettyRequest.PostDate,
            Barge = jettyRequest.Barge,
            PortOrigin = jettyRequest.PortOrigin,
            DestinationPort = jettyRequest.DestinationPort,
            CreationTime = jettyRequest.CreationTime,
            CreatorId = jettyRequest.CreatorId,
            LastModificationTime = jettyRequest.LastModificationTime,
            LastModifierId = jettyRequest.LastModifierId,
            IsDeleted = jettyRequest.IsDeleted,
            DeleterId = jettyRequest.DeleterId,
            DeletionTime = jettyRequest.DeletionTime,
            // Intentionally NOT mapping Items to avoid circular reference
            Items = new List<JettyRequestItemDto>()
        };
    }

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(JettyRequestItem.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(JettyRequestItem.JettyRequestId))] // Don't map JettyRequestId - it's set manually
    [MapperIgnoreTarget(nameof(JettyRequestItem.LetterDate))] // Handle LetterDate conversion manually
    [MapperIgnoreTarget(nameof(JettyRequestItem.JettyRequest))] // Navigation property
    [MapperIgnoreTarget(nameof(JettyRequestItem.IsDeleted))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.DeleterId))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.DeletionTime))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.LastModificationTime))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.LastModifierId))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.CreationTime))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.CreatorId))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.ExtraProperties))]
    [MapperIgnoreTarget(nameof(JettyRequestItem.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(CreateUpdateJettyRequestItemDto.JettyRequestId))] // Don't map JettyRequestId from DTO
    [MapperIgnoreSource(nameof(CreateUpdateJettyRequestItemDto.LetterDate))] // Handle LetterDate conversion manually
    private partial void MapToEntityInternal(CreateUpdateJettyRequestItemDto dto, JettyRequestItem entity);

    // Public method with custom LetterDate parsing
    public void MapToEntity(CreateUpdateJettyRequestItemDto dto, JettyRequestItem entity)
    {
        MapToEntityInternal(dto, entity);
        // Parse LetterDate from string to DateTime
        if (!string.IsNullOrEmpty(dto.LetterDate))
        {
            if (DateTime.TryParse(dto.LetterDate, out var letterDate))
            {
                entity.LetterDate = letterDate;
            }
        }
        else
        {
            entity.LetterDate = null;
        }
    }

    // Custom mapping methods for complex scenarios
    public JettyRequestItem CreateEntityWithId(CreateUpdateJettyRequestItemDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (JettyRequestItem)Activator.CreateInstance(typeof(JettyRequestItem), true)!;

        // Set the ID using reflection
        var idProperty = typeof(JettyRequestItem).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        // Set the JettyRequestId manually if provided in the DTO
        if (dto.JettyRequestId.HasValue)
        {
            entity.JettyRequestId = dto.JettyRequestId.Value;
        }

        return entity;
    }

    // Map list of entities to DTOs using custom mapping
    public List<JettyRequestItemDto> MapToDtoList(List<JettyRequestItem> entities)
    {
        return entities.Select(MapToDto).ToList();
    }

    // Map IEnumerable for LINQ scenarios using custom mapping
    public IEnumerable<JettyRequestItemDto> MapToDtoEnumerable(IEnumerable<JettyRequestItem> entities)
    {
        return entities.Select(MapToDto);
    }
}