import { Button } from '@/components/ui/button';
import type { BusinessPartnerDto, MasterTenantDto } from '@/clientEkb/types.gen';
import React from 'react';
import { createRoot } from 'react-dom/client';
import type { Root } from 'react-dom/client';
import { AttachmentDialog } from '../attachment-dialog';
import Handsontable from 'handsontable';
import type { QueryClient } from '@tanstack/react-query';

// Type guard for custom property on td
function getRootContainer(td: unknown): Root | undefined {
  return typeof td === 'object' && td !== null && '_reactRootContainer' in td
    ? (td as { _reactRootContainer?: Root })._reactRootContainer
    : undefined;
}
function setRootContainer(td: unknown, root: Root) {
  if (typeof td === 'object' && td !== null) {
    (td as { _reactRootContainer?: Root })._reactRootContainer = root;
  }
}

// Renderer factory for attachment button
export const renderAttachmentButton = (queryClient: QueryClient) => {
  if (!queryClient) throw new Error('queryClient is required for renderAttachmentButton');
  return (
    _instance: Handsontable.Core | undefined,
    td: HTMLTableCellElement,
    _row: number,
    _col: number,
    _prop: string | number,
    _value: unknown,
    _cellProperties: Handsontable.CellProperties
  ) => {
    void _col;
    void _prop;
    void _value;
    void _cellProperties;

    // Check if there's already a React root for this td element
    let root = getRootContainer(td);

    if (!root) {
      // Only create a new root if one doesn't exist
      root = createRoot(td);
      setRootContainer(td, root);
    }

    // Create a wrapper component to handle the dialog state
    const AttachmentButton = () => {
      const [open, setOpen] = React.useState(false);
      // Get the vessel ID to fetch fresh data
      const rowDataRaw = _instance?.getSourceDataAtRow?.(_row);
      const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};
      const vesselId = rowData.vesselId || rowData.id;

      // Get fresh data from the query cache first, then fallback to Handsontable data
      const freshData = queryClient.getQueryData(['import-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;
      const freshAttachments = freshData?.items?.find((item) => item.id === rowData.id)?.attachments;
      const attachments = freshAttachments || _instance?.getDataAtRowProp(_row, 'attachments') || [];

      const itemName = _instance?.getDataAtRowProp(_row, 'itemName') || '';
      const referenceId = rowData.id ?? '';
      const documentReferenceId = rowData.docEntry ?? 0;

      // Callback to refresh the table data after successful upload
      const handleUploadSuccess = () => {
        // Get the current vessel ID from the row data
        const rowDataRaw = _instance?.getSourceDataAtRow?.(_row);
        const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};
        const vesselId = rowData.vesselId || rowData.id;

        // Invalidate the specific query with the vessel ID to refetch the vessel data with updated attachments
        if (vesselId) {
          queryClient.invalidateQueries({ queryKey: ['import-vessel', vesselId] });
        } else {
          // Fallback to invalidate all import-vessel queries
          queryClient.invalidateQueries({ queryKey: ['import-vessel'] });
        }

        // Wait for the query to complete and then force re-render
        setTimeout(() => {
          if (_instance) {
            try {
              // Get fresh data from the query cache and update Handsontable immediately
              const freshData = queryClient.getQueryData(['import-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;
              if (freshData && freshData.items) {
                const freshItems = freshData.items;
                const currentItemId = rowData.id;
                const freshItem = freshItems.find((item) => item.id === currentItemId);
                if (freshItem) {
                  // Update the specific row with fresh data immediately
                  _instance.setDataAtRowProp(_row, 'attachments', freshItem.attachments || []);
                }
              }

              // Force Handsontable to re-render the entire table to pick up updated data
              _instance.render();

              // Force a complete re-render of the cell to update the attachment count
              setTimeout(() => {
                if (_instance) {
                  try {
                    // Re-render the cell to update the attachment count
                    root.render(React.createElement(AttachmentButton));
                  } catch (error) {
                    console.warn('Error re-rendering cell:', error);
                  }
                }
              }, 50); // Reduced delay since we already updated the data
            } catch (error) {
              console.warn('Error re-rendering Handsontable:', error);
            }
          }
        }, 100); // Reduced delay to ensure query completion
      };

      return React.createElement(React.Fragment, null, [
        React.createElement(Button, {
          key: 'button',
          size: 'xs',
          variant: 'success',
          type: 'button',
          onClick: () => setOpen(true),
          'aria-label': 'View Attachments',
          disabled: !attachments || attachments.length === 0,
          children: `Attachment (${attachments?.length || 0})`
        }),
        React.createElement(AttachmentDialog, {
          key: 'dialog',
          open: open,
          onOpenChange: setOpen,
          attachments: attachments || [],
          title: `Attachments - ${itemName || 'Item'}`,
          queryClient,
          referenceId,
          documentReferenceId,
          defaultTabName: 'AGENT',
          docType: 'Export',
          transType: 'ExportDetails',
          tabName: 'AGENT',
          onUploadSuccess: handleUploadSuccess,
        })
      ]);
    };

    root.render(React.createElement(AttachmentButton));
    return td;
  };
};

export const getImportVesselColumns = (tenants: MasterTenantDto[], businessPartners: BusinessPartnerDto[], queryClient: QueryClient) => {
  // Extract tenant names for autocomplete source
  const tenantNames = tenants.map(t => t.name ?? '').filter(name => name !== '');

  // Extract business partner names for autocomplete source
  const businessPartnerNames = businessPartners.map(bp => bp.name ?? '').filter(name => name !== '');

  return [
    { data: 'id', title: 'Id', type: 'text', width: 200 },
    { data: 'docEntry', title: 'DocEntry', type: 'text', width: 200 },
    { data: 'concurrencyStamp', title: 'concurrencyStamp', type: 'text', width: 200 },
    {
      data: 'tenant',
      title: 'Tenant',
      type: 'autocomplete',
      width: 140,
      source: tenantNames,
      strict: false,
      allowInvalid: false,
      trimDropdown: false,
      visibleRows: 6,
    },
    {
      data: 'businessPartner',
      title: 'Business Partner',
      type: 'autocomplete',
      width: 290,
      wordWrap: true,
      source: businessPartnerNames,
      strict: false,
      allowInvalid: false,
      trimDropdown: false,
      visibleRows: 6,
    },
    { data: 'itemName', title: 'Item Name', wordWrap: false, type: 'text', width: 200 },
    { data: 'itemQty', title: 'Quantity', wordWrap: false, type: 'numeric', width: 80 },
    { data: 'unitQty', title: 'UOM', wordWrap: false, type: 'text', width: 150 },
    { data: 'remarks', title: 'Remark', wordWrap: false, type: 'text', width: 120 },
    { data: 'letterNo', title: 'Letter No', wordWrap: false, type: 'text', width: 120 },
    { data: 'letterDate', title: 'Letter Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'noBl', title: 'No BL', wordWrap: false, type: 'text', width: 120 },
    // { data: 'dateBl', title: 'Date BL', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'ajuNo', title: 'AJU No', wordWrap: false, type: 'text', width: 120 },
    // { data: 'regNo', title: 'Reg No', wordWrap: false, type: 'text', width: 120 },
    // { data: 'regDate', title: 'Reg Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'sppbNo', title: 'SPPB No', wordWrap: false, type: 'text', width: 120 },
    // { data: 'sppbDate', title: 'SPPB Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'sppdNo', title: 'SPPD No', wordWrap: false, type: 'text', width: 120 },
    // { data: 'sppdDate', title: 'SPPD Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'grossWeight', title: 'Gross Weight', wordWrap: false, type: 'numeric', width: 100 },
    { data: 'unitWeight', title: 'Unit Weight', wordWrap: false, type: 'text', width: 100 },
    { data: 'attachments', title: 'Attachment', width: 100, renderer: renderAttachmentButton(queryClient), readOnly: true, filterable: false },
  ];
}; 